import 'dart:math';

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_service}
/// Service that handles face detection using Google ML Kit.
/// Provides real-time face detection and coverage analysis.
/// {@endtemplate}
class FaceDetectionService {
  /// {@macro face_detection_service}
  FaceDetectionService();

  final LoggerService _logger = LoggerService();
  
  FaceDetector? _faceDetector;
  bool _isInitialized = false;

  // Face guide configuration (oval area in center of screen)
  static const double _faceGuideWidthRatio = 0.7; // 70% of screen width
  static const double _faceGuideHeightRatio = 0.8; // 80% of screen height
  static const double _minimumCoverageThreshold = 80.0; // 80% coverage required

  /// Whether the service is initialized and ready for use
  bool get isInitialized => _isInitialized;

  /// Initializes the face detection service and ML Kit detector
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service already initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service initialization started',
      ),
    );

    try {
      // Configure face detector options
      final options = FaceDetectorOptions(
        enableContours: true,
        enableLandmarks: true,
        enableClassification: false,
        enableTracking: false,
        minFaceSize: 0.1, // Minimum face size (10% of image)
        performanceMode: FaceDetectorMode.fast,
      );

      _faceDetector = FaceDetector(options: options);
      _isInitialized = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialization completed',
          'Options: ${options.toString()}',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Detects faces in the current camera frame
  /// 
  /// This is a mock implementation that simulates face detection.
  /// In a real implementation, this would process the actual camera frame.
  Future<FaceDetectionResult?> detectFaceInCurrentFrame() async {
    if (!_isInitialized) {
      throw StateError('Face detection service not initialized');
    }

    try {
      // Mock face detection - simulate realistic detection results
      final random = Random();
      final timestamp = DateTime.now();

      // Simulate face detection with 85% success rate
      final faceDetected = random.nextDouble() > 0.15;
      
      if (!faceDetected) {
        return FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0.0,
          timestamp: timestamp,
        );
      }

      // Simulate single face detection (ideal case)
      final faceCount = 1;
      
      // Simulate coverage percentage (varies between 60-95%)
      final baseCoverage = 60.0 + (random.nextDouble() * 35.0);
      final coveragePercentage = baseCoverage.clamp(0.0, 100.0);

      // Simulate bounding box
      final boundingBox = _generateMockBoundingBox(random);

      // Simulate confidence (0.7-0.95 for detected faces)
      final confidence = 0.7 + (random.nextDouble() * 0.25);

      return FaceDetectionResult(
        faceDetected: true,
        faceCount: faceCount,
        coveragePercentage: coveragePercentage,
        timestamp: timestamp,
        boundingBox: boundingBox,
        confidence: confidence,
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection frame processing failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Validates if the face coverage meets the minimum threshold
  bool validateCoverage(FaceDetectionResult result) {
    return result.faceDetected && 
           result.faceCount == 1 && 
           result.coveragePercentage >= _minimumCoverageThreshold;
  }

  /// Calculates face coverage percentage within the guide area
  /// 
  /// This is a mock implementation. In a real implementation, this would
  /// calculate the intersection between the detected face bounding box
  /// and the face guide overlay area.
  double calculateFaceCoverage(FaceBoundingBox faceBounds, Size screenSize) {
    // Calculate face guide area (centered oval)
    final guideWidth = screenSize.width * _faceGuideWidthRatio;
    final guideHeight = screenSize.height * _faceGuideHeightRatio;
    final guideLeft = (screenSize.width - guideWidth) / 2;
    final guideTop = (screenSize.height - guideHeight) / 2;

    // Calculate intersection area
    final intersectionLeft = max(faceBounds.left, guideLeft);
    final intersectionTop = max(faceBounds.top, guideTop);
    final intersectionRight = min(faceBounds.right, guideLeft + guideWidth);
    final intersectionBottom = min(faceBounds.bottom, guideTop + guideHeight);

    if (intersectionLeft >= intersectionRight || intersectionTop >= intersectionBottom) {
      return 0.0; // No intersection
    }

    final intersectionArea = (intersectionRight - intersectionLeft) * 
                           (intersectionBottom - intersectionTop);
    final guideArea = guideWidth * guideHeight;

    return (intersectionArea / guideArea) * 100.0;
  }

  /// Gets the current face detection configuration
  Map<String, dynamic> getConfiguration() {
    return {
      'isInitialized': _isInitialized,
      'faceGuideWidthRatio': _faceGuideWidthRatio,
      'faceGuideHeightRatio': _faceGuideHeightRatio,
      'minimumCoverageThreshold': _minimumCoverageThreshold,
      'detectorOptions': _faceDetector != null ? {
        'enableContours': true,
        'enableLandmarks': true,
        'enableClassification': false,
        'enableTracking': false,
        'minFaceSize': 0.1,
        'performanceMode': 'fast',
      } : null,
    };
  }

  /// Updates the face detection configuration
  Future<void> updateConfiguration(Map<String, dynamic> config) async {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection configuration update requested',
        'Config: $config',
      ),
    );

    // In a real implementation, this would update detector settings
    // For now, we just log the configuration change
  }

  /// Generates a mock bounding box for testing
  FaceBoundingBox _generateMockBoundingBox(Random random) {
    // Simulate face in center area of screen
    final left = 100.0 + (random.nextDouble() * 200.0);
    final top = 200.0 + (random.nextDouble() * 300.0);
    final width = 150.0 + (random.nextDouble() * 100.0);
    final height = 180.0 + (random.nextDouble() * 120.0);

    return FaceBoundingBox(
      left: left,
      top: top,
      width: width,
      height: height,
    );
  }

  /// Disposes of the face detection service and releases resources
  Future<void> dispose() async {
    if (!_isInitialized) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal skipped - not initialized',
        ),
      );
      return;
    }

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Face detection service disposal started',
      ),
    );

    try {
      await _faceDetector?.close();
      _faceDetector = null;
      _isInitialized = false;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service disposal completed',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'Face detection service disposal error: $error',
        ),
        error,
        stackTrace,
      );
      // Don't rethrow disposal errors
    }
  }
}

/// {@template size}
/// Simple size class for width and height dimensions.
/// {@endtemplate}
class Size {
  /// {@macro size}
  const Size(this.width, this.height);

  /// Width dimension
  final double width;

  /// Height dimension  
  final double height;

  @override
  String toString() => 'Size($width, $height)';
}
